<template>
  <div class="table-container">
    <div class="toolbar">
      <div class="toolbar-actions">
        <a-button type="primary" @click="submit">提交</a-button>
      </div>
      <div class="toolbar-filters">
        <span>日期范围:</span>
        <a-date-picker :presets="presets" v-model:value="dateRange" @change="dateRangeChange" :allowClear="false" />
      </div>
    </div>
    <div class="flex justify-between items-center">
      <div></div>
      <div class="title items-center">
        <div>其他费用明细表</div>
        <div v-show="!isSave" class="text-red">*</div>
      </div>
      <div class="operator">
        <a-tooltip>
          <template #title>自动填充</template>
          <SisternodeOutlined class="mr-6 text-lg" @click="clickAutoFill" />
        </a-tooltip>
        <div class="mr-10"></div>
      </div>
    </div>
    <div class="grid-table">
      <!-- 表头 -->
      <div class="row header">
        <div>项目</div>
        <div>金额(万元)</div>
        <div>月累计</div>
      </div>
      <!-- 数据行 -->
      <div class="row" v-for="d in pageData" :key="d.name">
        <!--  项目  -->
        <div>{{ d.name }}</div>
        <!--  当日金额  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="d.je" /></div>
        <!--  月累计  -->
        <div>{{ formatNumber(d.ylj + d.je) }}</div>
      </div>
      <!--  合计      -->
      <div class="row">
        <div>合计</div>
        <!--  当日数  -->
        <div>{{ formatNumber(hj.je) }}</div>
        <!--  月累计  -->
        <div>{{ formatNumber(hj.ylj + hj.je) }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup name="cw-qtfy">
  import { computed, onMounted, ref, toRaw, watch } from 'vue';
  import { CloudSyncOutlined, SisternodeOutlined } from '@ant-design/icons-vue';
  // 项目字典
  import { defHttp } from '@/utils/http/axios';
  import dayjs, { Dayjs } from 'dayjs';
  import { formatNumber } from '@/utils/showUtils';
  import { message } from 'ant-design-vue';

  // 获取数据
  const pageData = ref<Array<{ name: string; je: number; ylj: number }>>([]);
  const show = ref(false);
  let oldData: String = '';
  let isSave = ref(true);
  // 日期选择
  const dateRange = ref(dayjs(new Date()));
  onMounted(async () => {
    await httpGetData(dateRange.value);
    show.value = true;
  });
  // 是否保存
  watch(
    () => [pageData.value],
    (newValue) => {
      isSave.value = oldData === JSON.stringify(toRaw(newValue));
    },
    { deep: true }
  );
  // 日期选择器变化
  const dateRangeChange = async (v) => {
    show.value = false;
    await httpGetData(dayjs(v));
    show.value = true;
  };

  // 提交
  const submit = () => {
    // 提交数据
    defHttp.post({
      url: 'qtfy/cwQtfy/submit',
      params: {
        submitDate: dayjs(dateRange.value).format('YYYY-MM-DD'),
        rows: pageData.value,
      },
    });
  };

  const clickAutoFill = async () => {
    const res = await defHttp.get({
      url: 'qtfy/cwQtfy//autoFill',
      params: {
        queryDate: dayjs(dateRange.value).format('YYYY-MM-DD'),
      },
    });
    if (res == null) {
      message.warn('无数据填充');
      return;
    }
    pageData.value = res.rows;
    message.info('填充完成');
  };

  // =====================================公共函数
  const httpGetData = async (date: Dayjs) => {
    let queryDate = dayjs(date).format('YYYY-MM-DD');
    const res = await defHttp.get({ url: '/qtfy/cwQtfy/query', params: { queryDate } });
    pageData.value = res.rows;
    oldData = JSON.stringify([pageData.value]);
  };

  const hj = computed(() => {
    return {
      je: pageData.value.reduce((acc, cur) => acc + cur.je, 0),
      ylj: pageData.value.reduce((acc, cur) => acc + cur.ylj, 0),
    };
  });

  const presets = ref([
    { label: '昨天', value: dayjs().add(-1, 'd') },
    { label: '明天', value: dayjs().add(1, 'd') },
    { label: '上周', value: dayjs().add(-7, 'd') },
    { label: '上个月', value: dayjs().add(-1, 'month') },
  ]);
</script>
<style lang="less" scoped>
  @import url(@/views/cw/public/style/ant-input.css);

  :deep(.custom-center-text) {
    width: 30%;
  }

  /* 容器样式 */
  .table-container {
    width: 100%;
    //max-width: 1500px;
    margin: 0 auto;
    background: linear-gradient(180deg, #f5f7fa 0%, #eef1f5 100%);
    padding: 20px;
    border-radius: 12px;
    box-shadow:
      0 4px 6px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.08);
  }

  /* 工具栏样式 */
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #eaeaea;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  /* 工具栏动作区域 */
  .toolbar-actions {
    display: flex;
    gap: 10px;
  }

  /* 提交按钮样式 */
  .submit-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .submit-button:hover {
    background-color: #0056b3;
  }

  /* 工具栏筛选区域 */
  .toolbar-filters {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .toolbar-filters span {
    font-weight: bold;
  }

  /* 时间选择器样式 */
  .ant-calendar-picker-input.ant-input {
    height: 36px;
    padding: 0 10px;
    font-size: 14px;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s;
  }

  .ant-calendar-picker-input.ant-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
  }

  /* 容器样式 */
  .table-container {
    width: 100%;
    //max-width: 1800px;
    margin: 0 auto;
    background: linear-gradient(180deg, #f5f7fa 0%, #eef1f5 100%);
    padding: 20px;
    border-radius: 12px;
    box-shadow:
      0 4px 6px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.08);
  }

  /* 标题样式 */
  .title {
    display: flex;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    text-align: center;
    padding: 15px 0;
  }

  /* 表格基础样式 */
  .grid-table {
    display: grid;
    gap: 5px;
    border-radius: 6px;
    overflow: hidden;
    background-color: #ffffff; /* 确保表格内部有清晰对比 */
  }

  /* 行样式 */
  .row {
    display: contents; /* 让子元素直接成为网格项 */
  }

  .custom-center-text :deep(.ant-input-number-input) {
    text-align: center !important;
  }

  :deep(.custom-center-text) {
    text-align: center !important;
  }

  /* 表头样式 */
  .header {
    font-weight: bold;
    color: white;
    background: linear-gradient(135deg, #007bff, #1e90ff);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  /* 定义每一列的宽度 */
  .grid-table > div {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 3 列 */
    padding: 10px;
    text-align: center;
    border-right: 1px solid #eaeaea;
    transition: background-color 0.2s;

    &:last-child {
      border-right: none;
    }
  }

  /* 单元格悬停效果 */
  .grid-table > div:hover {
    background-color: #e9ecef;
  }

  /* 小计单元格样式 */
  .subtotal .span-3 {
    grid-column: span 3; /* 跨越3列 */
    background-color: #e6ffed;
    color: #28a745;
    font-weight: bold;
  }

  /* 小计行项目名称样式 */
  .subtotal > div:first-child {
    font-weight: bold;
  }

  /* 行交替背景色 */
  .grid-table > :nth-child(even) {
    background-color: #f8f9fa;
  }

  .grid-table > :nth-child(odd) {
    background-color: #ffffff;
  }
</style>
