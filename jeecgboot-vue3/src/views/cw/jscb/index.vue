<template>
  <div class="table-container">
    <div class="toolbar">
      <div class="toolbar-actions">
        <a-button type="primary" @click="submit">提交</a-button>
      </div>
      <div class="toolbar-filters">
        <span>日期范围:</span>
        <a-date-picker v-model:value="dateRange" @change="dateRangeChange" :allowClear="false" />
      </div>
    </div>
    <div class="flex justify-between items-center">
      <div></div>
      <div class="title items-center">
        <div>金属成本表</div>
        <div v-show="!isSave" class="text-red">*</div>
      </div>
      <div class="operator">
        <a-tooltip>
          <!-- <template #title>自动填充</template>
          <SisternodeOutlined class="mr-6 text-lg" @click="clickAutoFill" /> -->
        </a-tooltip>
        <!--        <a-tooltip>-->
        <!--          <template #title>自动保存</template>-->
        <!--          <CloudSyncOutlined class="mr-6 text-lg" />-->
        <!--        </a-tooltip>-->
        <div class="mr-10"></div>
      </div>
    </div>
    <div class="grid-table">
      <!-- 表头 -->
      <div class="row header">
        <div>德兴铜矿铜硫钼分离</div>
        <div>分摊系数1</div>
        <div>分摊系数2</div>
        <div>分摊系数3</div>
        <div>累计金属成本总额(万元)</div>
        <div>累计金属量</div>
        <div>当日金属量</div>
        <div>金属单位成本(万元)</div>
      </div>
      <!-- 数据行 -->
      <div class="row">
        <!--  项目  -->
        <div>{{ tData.name }}</div>
        <!--  分摊系数1  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="tData.ftxs1" />%</div>
        <!--  分摊系数2  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="tData.ftxs2" />%</div>
        <!--  分摊系数3  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="tData.ftxs3" />%</div>
        <!--  累计金属成本总额     -->
        <div>{{ formatNumber(jscb.t) }}</div>
        <!--  累计金属量     -->
        <div>{{ formatNumber(tData.ljs) }}</div>
        <!--   当日金属量     -->
        <div>{{ formatNumber(tData.drs) }}</div>
        <!--   金属单位成本     -->
        <div>{{ formatNumber(jscb.t / tData.ljs) }}</div>
      </div>
      <div class="row">
        <!--  项目  -->
        <div>{{ jData.name }}</div>
        <!--  分摊系数1  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="jData.ftxs1" />%</div>
        <!--  分摊系数2  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="jData.ftxs2" />%</div>
        <!--  分摊系数3  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="jData.ftxs3" />%</div>
        <!--  累计金属成本总额     -->
        <div>{{ formatNumber(jscb.j) }}</div>
        <!--  累计金属量     -->
        <div>{{ formatNumber(jData.ljs) }}</div>
        <!--   当日金属量     -->
        <div>{{ formatNumber(jData.drs) }}</div>
        <!--   金属单位成本     -->
        <div>{{ formatNumber(jscb.j / jData.ljs) }}</div>
      </div>
      <div class="row">
        <!--  项目  -->
        <div>{{ yData.name }}</div>
        <!--  分摊系数1  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="yData.ftxs1" />%</div>
        <!--  分摊系数2  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="yData.ftxs2" />%</div>
        <!--  分摊系数3  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="yData.ftxs3" />%</div>
        <!--  累计金属成本总额     -->
        <div>{{ formatNumber(jscb.y) }}</div>
        <!--  累计金属量     -->
        <div>{{ formatNumber(yData.ljs) }}</div>
        <!--   当日金属量     -->
        <div>{{ formatNumber(yData.drs) }}</div>
        <!--   金属单位成本     -->
        <div>{{ formatNumber(jscb.y / yData.ljs) }}</div>
      </div>
      <div class="row">
        <!--  项目  -->
        <div>{{ tjkData.name }}</div>
        <!--  分摊系数1  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="tjkData.ftxs1" />%</div>
        <!--  分摊系数2  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="tjkData.ftxs2" />%</div>
        <!--  分摊系数3  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="tjkData.ftxs3" />%</div>
        <!--  累计金属成本总额     -->
        <div>{{ formatNumber(jscb.tjk) }}</div>
        <!--  累计金属量     -->
        <div>{{ formatNumber(tjkData.ljs) }}</div>
        <!--   当日金属量     -->
        <div>{{ formatNumber(tjkData.drs) }}</div>
        <!--   金属单位成本     -->
        <div>{{ formatNumber(jscb.tjk / tjkData.ljs) }}</div>
      </div>
      <div class="row">
        <!--  项目  -->
        <div>{{ ljkData.name }}</div>
        <!--  分摊系数1  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="ljkData.ftxs1" />%</div>
        <!--  分摊系数2  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="ljkData.ftxs2" />%</div>
        <!--  分摊系数3  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="ljkData.ftxs3" />%</div>
        <!--  累计金属成本总额     -->
        <div>{{ formatNumber(jscb.ljk) }}</div>
        <!--  累计金属量     -->
        <div>{{ formatNumber(ljkData.ljs) }}</div>
        <!--   当日金属量     -->
        <div>{{ formatNumber(ljkData.drs) }}</div>
        <!--   金属单位成本     -->
        <div>{{ formatNumber(jscb.ljk / ljkData.ljs) }}</div>
      </div>
      <div class="row">
        <!--  项目  -->
        <div>{{ mjkData.name }}</div>
        <!--  分摊系数1  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="mjkData.ftxs1" />%</div>
        <!--  分摊系数2  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="mjkData.ftxs2" />%</div>
        <!--  分摊系数3  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="mjkData.ftxs3" />%</div>
        <!--  累计金属成本总额     -->
        <div>{{ formatNumber(jscb.mjk) }}</div>
        <!--  累计金属量     -->
        <div>{{ formatNumber(mjkData.ljs) }}</div>
        <!--   当日金属量     -->
        <div>{{ formatNumber(mjkData.drs) }}</div>
        <!--   金属单位成本     -->
        <div>{{ formatNumber(jscb.mjk / mjkData.ljs) }}</div>
      </div>
      <div class="row subtotal">
        <!--  项目  -->
        <div class="span-2">1、铜精矿内调运费 </div>
        <!--   值     -->
        <div class="span"><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="cwData.dyf" /></div>
      </div>
      <div class="row subtotal">
        <!--  项目  -->
        <div class="span-2">2、精尾作业成本（吨矿成本不含酸性水成本） </div>
        <!--   值     -->
        <div class="span"><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="cwData.zycb" /></div>
      </div>
      <div class="row subtotal">
        <!--  项目  -->
        <div class="span-2">3、黄金免税分摊 </div>
        <!--   值     -->
        <div class="span"><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="cwData.msft" /></div>
      </div>
      <div class="row subtotal">
        <!--  项目  -->
        <div class="span-2">4、精尾厂COD处理成本 </div>
        <!--   值     -->
        <div class="span"><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="cwData.clcb" /></div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup name="cw-jscb">
  import { computed, onMounted, ref, toRaw, watch } from 'vue';
  // 项目字典
  import { defHttp } from '@/utils/http/axios';
  import dayjs, { Dayjs } from 'dayjs';
  import { formatNumber } from '@/utils/showUtils';
  import { CloudSyncOutlined, SisternodeOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';

  type jlfx = { name: string; sj: number; jh: number; type: string };

  // 获取数据
  const tData = ref<any>({});
  const jData = ref<any>({});
  const yData = ref<any>({});
  const tjkData = ref<any>({});
  const ljkData = ref<any>({});
  const mjkData = ref<any>({});
  const cwData = ref<any>({});
  const show = ref(false);
  let oldData: String = '';
  let isSave = ref(true);
  // 日期选择
  const dateRange = ref(dayjs(new Date()));
  onMounted(async () => {
    await httpGetData(dateRange.value);
  });
  // 是否保存
  watch(
    () => [tData.value, jData.value, yData.value, tjkData.value, ljkData.value, mjkData.value, cwData.value],
    (newValue) => {
      isSave.value = oldData === JSON.stringify(toRaw(newValue));
    },
    { deep: true }
  );
  // 日期选择器变化
  const dateRangeChange = async (v) => {
    await httpGetData(dayjs(v));
  };
  // 机关财务数据
  const jscb = computed(() => {
    let tjk =
      (cwData.value.zcb - cwData.value.dyf - cwData.value.zycb - cwData.value.msft - cwData.value.clcb) * (tjkData.value.ftxs1 * 0.01) +
      cwData.value.zycb * tjkData.value.ftxs2 * 0.01 +
      cwData.value.dyf +
      cwData.value.msft;
    return {
      t: (tjk - cwData.value.msft) * (tData.value.ftxs3 * 0.01),
      j: (tjk - cwData.value.msft) * (jData.value.ftxs3 * 0.01) + cwData.value.msft,
      y: (tjk - cwData.value.msft) * (yData.value.ftxs3 * 0.01),
      tjk: tjk,
      ljk:
        (cwData.value.zcb - cwData.value.dyf - cwData.value.zycb - cwData.value.msft - cwData.value.clcb) * (ljkData.value.ftxs1 * 0.01) +
        cwData.value.zycb * (ljkData.value.ftxs2 * 0.01),
      mjk:
        (cwData.value.zcb - cwData.value.dyf - cwData.value.zycb - cwData.value.msft - cwData.value.clcb) * (mjkData.value.ftxs1 * 0.01) +
        cwData.value.clcb,
    };
  });

  const clickAutoFill = async () => {
    const res = await defHttp.get({
      url: '/jscb/cwJscb/autoFill',
      params: {
        queryDate: dayjs(dateRange.value).format('YYYY-MM-DD'),
      },
    });
    if (res == null) {
      message.warn('无数据填充');
      return;
    }
    packResult(res);
    message.info('填充完成');
  };

  // =====================================公共函数
  // 提交
  const submit = async () => {
    // 提交数据
    await defHttp.post({
      url: '/jscb/cwJscb/submit',
      params: {
        submitDate: dayjs(dateRange.value).format('YYYY-MM-DD'),
        rows: [tData.value, jData.value, yData.value, tjkData.value, ljkData.value, mjkData.value],
        dyf: cwData.value.dyf,
        zycb: cwData.value.zycb,
        msft: cwData.value.msft,
        clcb: cwData.value.clcb,
      },
    });
    httpGetData(dateRange.value);
  };

  const httpGetData = async (date: Dayjs) => {
    show.value = false;
    let queryDate = dayjs(date).format('YYYY-MM-DD');
    const res = await defHttp.get({ url: '/jscb/cwJscb/query', params: { queryDate } });
    packResult(res);
    oldData = JSON.stringify([tData.value, jData.value, yData.value, tjkData.value, ljkData.value, mjkData.value, cwData.value]);
    show.value = true;
  };

  const packResult = (res) => {
    tData.value = res.rows.filter((d: jlfx) => d.type === 't')[0] || {};
    jData.value = res.rows.filter((d: jlfx) => d.type === 'j')[0] || {};
    yData.value = res.rows.filter((d: jlfx) => d.type === 'y')[0] || {};
    tjkData.value = res.rows.filter((d: jlfx) => d.type === 'tjk')[0] || {};
    ljkData.value = res.rows.filter((d: jlfx) => d.type === 'ljk')[0] || {};
    mjkData.value = res.rows.filter((d: jlfx) => d.type === 'mjk')[0] || {};
    cwData.value = {
      dyf: res.dyf,
      zycb: res.zycb,
      msft: res.msft,
      clcb: res.clcb,
      zcb: res.zcb,
    };
  };
</script>
<style lang="less" scoped>
  @import url(@/views/cw/public/style/ant-input.css);

  :deep(.custom-center-text) {
    width: 15%;
  }

  /* 小计单元格样式 */
  .span {
    grid-column: span 6; /* 跨越6列 */
    background-color: #e6ffed;
    color: #28a745;
    font-weight: bold;
  }

  .span-2 {
    grid-column: span 2;
  }

  /* 容器样式 */
  .table-container {
    width: 100%;
    //max-width: 1500px;
    margin: 0 auto;
    background: linear-gradient(180deg, #f5f7fa 0%, #eef1f5 100%);
    padding: 20px;
    border-radius: 12px;
    box-shadow:
      0 4px 6px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.08);
  }

  /* 工具栏样式 */
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #eaeaea;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  /* 工具栏动作区域 */
  .toolbar-actions {
    display: flex;
    gap: 10px;
  }

  /* 提交按钮样式 */
  .submit-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .submit-button:hover {
    background-color: #0056b3;
  }

  /* 工具栏筛选区域 */
  .toolbar-filters {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .toolbar-filters span {
    font-weight: bold;
  }

  /* 时间选择器样式 */
  .ant-calendar-picker-input.ant-input {
    height: 36px;
    padding: 0 10px;
    font-size: 14px;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s;
  }

  .ant-calendar-picker-input.ant-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
  }

  /* 容器样式 */
  .table-container {
    width: 100%;
    //max-width: 1800px;
    margin: 0 auto;
    background: linear-gradient(180deg, #f5f7fa 0%, #eef1f5 100%);
    padding: 20px;
    border-radius: 12px;
    box-shadow:
      0 4px 6px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.08);
  }

  /* 标题样式 */
  .title {
    display: flex;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    text-align: center;
    padding: 15px 0;
  }

  /* 表格基础样式 */
  .grid-table {
    display: grid;
    gap: 5px;
    border-radius: 6px;
    overflow: hidden;
    background-color: #ffffff; /* 确保表格内部有清晰对比 */
  }

  /* 行样式 */
  .row {
    display: contents; /* 让子元素直接成为网格项 */
  }

  .custom-center-text :deep(.ant-input-number-input) {
    text-align: center !important;
  }

  :deep(.custom-center-text) {
    text-align: center !important;
  }

  /* 表头样式 */
  .header {
    font-weight: bold;
    color: white;
    background: linear-gradient(135deg, #007bff, #1e90ff);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  /* 定义每一列的宽度 */
  .grid-table > div {
    display: grid;
    grid-template-columns: repeat(8, 1fr); /* 8 列 */
    padding: 10px;
    text-align: center;
    border-right: 1px solid #eaeaea;
    transition: background-color 0.2s;

    &:last-child {
      border-right: none;
    }
  }

  /* 单元格悬停效果 */
  .grid-table > div:hover {
    background-color: #e9ecef;
  }

  /* 小计单元格样式 */
  .subtotal .span-3 {
    grid-column: span 3; /* 跨越3列 */
    background-color: #e6ffed;
    color: #28a745;
    font-weight: bold;
  }

  /* 小计行项目名称样式 */
  .subtotal > div:first-child {
    font-weight: bold;
  }

  /* 行交替背景色 */
  .grid-table > :nth-child(even) {
    background-color: #f8f9fa;
  }

  .grid-table > :nth-child(odd) {
    background-color: #ffffff;
  }
</style>
