<template>
  <div class="table-container">
    <div class="toolbar">
      <div class="toolbar-actions">
        <a-button type="primary" @click="submit">提交</a-button>
      </div>
      <div class="toolbar-filters">
        <span>日期范围:</span>
        <a-date-picker v-model:value="dateRange" :presets="presets" @change="dateRangeChange" :allowClear="false" />
      </div>
    </div>
    <div class="flex justify-between items-center">
      <div></div>
      <div class="title items-center">
        <div>机关综合成本表</div>
        <div v-show="!isSave" class="text-red">*</div>
      </div>
      <div class="operator">
        <a-tooltip>
          <template #title>自动填充</template>
          <SisternodeOutlined class="mr-6 text-lg" @click="clickAutoFill" />
        </a-tooltip>
        <div class="mr-10"></div>
      </div>
    </div>
    <div class="grid-table">
      <!-- 表头 -->
      <div class="row header">
        <div>项目</div>
        <div>单位</div>
        <div>当日数</div>
        <div>月累计</div>
      </div>
      <!--   数据   -->
      <div class="row subtotal" v-for="item in part1" :key="item.name">
        <!--  项目  -->
        <div>{{ item?.name }}</div>
        <!--  单位  -->
        <div>{{ item?.unit }}</div>
        <!--  当日数  -->
        <div>
          <a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="item.drs" />
        </div>
        <!--  月累计  -->
        <div>{{ formatNumber(item?.ylj + item?.drs) }}</div>
      </div>
      <div class="row subtotal">
        <!--  项目  -->
        <div>动力</div>
        <!--  单位  -->
        <div>万元</div>
        <!--  当日数  -->
        <div>{{ formatNumber(part2.reduce((acc, cur) => acc + cur?.drs, 0)) }}</div>
        <!--  月累计  -->
        <div>{{ formatNumber(part2.reduce((acc, cur) => acc + cur?.ylj, 0)) }}</div>
      </div>
      <div class="row subtotal" v-for="item in part2" :key="item.name">
        <!--  项目  -->
        <div>{{ item?.name }}</div>
        <!--  单位  -->
        <div>{{ item?.unit }}</div>
        <!--  当日数  -->
        <div>
          <a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="item.drs" />
        </div>
        <!--  月累计  -->
        <div>{{ formatNumber(item?.ylj + item?.drs) }}</div>
      </div>
      <div class="row subtotal" v-for="item in part3" :key="item.name">
        <!--  项目  -->
        <div>{{ item?.name }}</div>
        <!--  单位  -->
        <div>{{ item?.unit }}</div>
        <!--  当日数  -->
        <div>
          <a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="item.drs" />
        </div>
        <!--  月累计  -->
        <div>{{ formatNumber(item?.ylj + item?.drs) }}</div>
      </div>
      <div class="row subtotal">
        <!--  项目  -->
        <div>直接成本</div>
        <!--  单位  -->
        <div>万元</div>
        <!--  当日数  -->
        <div>{{ formatNumber(zjcb.drs) }}</div>
        <!--  月累计  -->
        <div>{{ formatNumber(zjcb.ylj) }} </div>
      </div>
      <div class="row subtotal" v-for="item in part4" :key="item.name">
        <!--  项目  -->
        <div>{{ item?.name }}</div>
        <!--  单位  -->
        <div>{{ item?.unit }}</div>
        <!--  当日数  -->
        <div>
          <a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="item.drs" />
        </div>
        <!--  月累计  -->
        <div>{{ formatNumber(item?.ylj + item?.drs) }}</div>
      </div>
      <!--   合计   -->
      <div class="row subtotal">
        <div>制造成本</div>
        <div>万元</div>
        <!--  当日数  -->
        <div>{{ formatNumber(hj.drs) }}</div>
        <!--  月累计  -->
        <div>{{ formatNumber(hj.ylj) }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup name="cw-jg-zhcb">
  import { computed, onMounted, Ref, ref, watch } from 'vue';
  // 项目字典
  import { defHttp } from '@/utils/http/axios';
  import dayjs, { Dayjs } from 'dayjs';
  import { formatNumber, getSafeValue } from '@/utils/showUtils';
  import { CloudSyncOutlined, SisternodeOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import Big from 'big.js';

  // 获取数据
  const rows: Ref<Array<any>> = ref([]);
  const part1: Ref<any> = ref([]);
  const part2: Ref<any> = ref([]);
  const part3: Ref<any> = ref([]);
  const part4: Ref<any> = ref([]);
  const show = ref(false);
  const hj = computed(() => {
    const drs = rows.value
      .reduce((acc, cur) => acc.plus(getSafeValue(cur?.drs)), new Big(0))
      .minus(rows.value.filter((item) => item?.type === 'unrelated').reduce((acc, cur) => acc.plus(getSafeValue(cur?.drs)), new Big(0)))
      .toNumber();

    const rys = rows.value.reduce((acc, cur) => acc.plus(getSafeValue(cur?.rys)), new Big(0)).toNumber();

    const ylj = rows.value
      .reduce((acc, cur) => acc.plus(getSafeValue(cur?.ylj)).plus(getSafeValue(cur?.drs)), new Big(0))
      .minus(
        rows.value
          .filter((item) => item?.type === 'unrelated')
          .reduce((acc, cur) => acc.plus(getSafeValue(cur?.ylj)).plus(getSafeValue(cur?.drs)), new Big(0))
      )
      .toNumber();

    const jcbNumerator = getSafeValue(drs).minus(getSafeValue(rys));
    const jcbDenominator = getSafeValue(rys);

    const jcb = jcbDenominator.eq(0) ? 0 : jcbNumerator.times(100).div(jcbDenominator).toNumber();

    return {
      drs: isNaN(drs) ? 0 : drs,
      ylj: isNaN(ylj) ? 0 : ylj,
      rys: isNaN(rys) ? 0 : rys,
      jcb: isNaN(jcb) ? 0 : jcb,
    };
  });

  const zjcb = computed(() => {
    if (!part1.value.length || !part2.value.length || !part3.value.length) {
      return {
        drs: 0,
        ylj: 0,
      };
    }
    const drs = [
      part1.value.reduce((acc, cur) => acc.plus(getSafeValue(cur?.drs)), new Big(0)),
      part2.value.reduce((acc, cur) => acc.plus(getSafeValue(cur?.drs)), new Big(0)),
      part3.value.reduce((acc, cur) => acc.plus(getSafeValue(cur?.drs)), new Big(0)),
    ]
      .reduce((acc, cur) => acc.plus(cur), new Big(0))
      .toNumber();

    const ylj = [
      part1.value.reduce((acc, cur) => acc.plus(getSafeValue(cur?.ylj)), new Big(0)),
      part2.value.reduce((acc, cur) => acc.plus(getSafeValue(cur?.ylj)), new Big(0)),
      part3.value.reduce((acc, cur) => acc.plus(getSafeValue(cur?.ylj)), new Big(0)),
    ]
      .reduce((acc, cur) => acc.plus(cur), new Big(0))
      .toNumber();

    return {
      drs: isNaN(drs) ? 0 : drs,
      ylj: isNaN(ylj) ? 0 : ylj,
    };
  });
  const isSave = ref(true);
  // 日期选择
  const dateRange = ref(dayjs(new Date()));
  onMounted(async () => {
    await httpGetData(dateRange.value);
  });
  // 是否保存
  let firstWatch = true;
  watch(
    () => hj,
    () => {
      if (firstWatch) {
        firstWatch = false;
        return;
      }
      isSave.value = false;
    },
    { deep: true }
  );
  // 日期选择器变化
  const dateRangeChange = async (v) => {
    await httpGetData(dayjs(v));
  };
  // 提交
  const submit = () => {
    let recordTime = dayjs(dateRange.value).format('YYYY-MM-DD');
    // 提交数据
    defHttp.post({
      url: 'jg/zhcb/submit',
      params: {
        submitDate: recordTime,
        rows: [...part1.value, ...part2.value, ...part3.value, ...part4.value],
      },
    });
    let cbgc: any = [
      {
        type: 'cl',
        cb: rows.value.filter((item) => item.type == 'cl').reduce((acc, cur) => acc.plus(getSafeValue(cur?.drs)), new Big(0)),
      },
      {
        type: 'bj',
        cb: rows.value.filter((item) => item.type == 'bj').reduce((acc, cur) => acc.plus(getSafeValue(cur?.drs)), new Big(0)),
      },
      {
        type: 'rl',
        cb: rows.value.filter((item) => item.type == 'rl').reduce((acc, cur) => acc.plus(getSafeValue(cur?.drs)), new Big(0)),
      },
      {
        type: 'dl',
        cb: rows.value.filter((item) => item.type == 'd' || item.type == 's').reduce((acc, cur) => acc.plus(getSafeValue(cur?.drs)), new Big(0)),
      },
      {
        type: 'gz',
        cb: rows.value.filter((item) => item.type == 'gz' || item.type == 'gzxfy').reduce((acc, cur) => acc.plus(getSafeValue(cur?.drs)), new Big(0)),
      },
      {
        type: 'zzfy',
        cb: rows.value.filter((item) => item.type == 'zzfy').reduce((acc, cur) => acc.plus(getSafeValue(cur?.drs)), new Big(0)),
      },
      {
        type: 'zjf',
        cb: rows.value.filter((item) => item.type == 'zjf').reduce((acc, cur) => acc.plus(getSafeValue(cur?.drs)), new Big(0)),
      },
    ];
    cbgc.forEach((item) => {
      item.name = 'jg';
      item.recordTime = recordTime;
    });
    defHttp.post(
      {
        url: 'cbgc/cwCbgc/addOrEdit',
        params: cbgc,
      },
      { successMessageMode: 'none' }
    );
    isSave.value = true;
  };

  const clickAutoFill = async () => {
    const res = await defHttp.get({
      url: 'jg/zhcb/autoFill',
      params: {
        queryDate: dayjs(dateRange.value).format('YYYY-MM-DD'),
      },
    });
    if (res == null) {
      message.warn('无数据填充');
      return;
    }
    rows.value.forEach((item) => {
      let find = res.rows.find((row) => row.name === item.name);
      item.drs = find?.drs;
    });
    part1.value.forEach((item) => {
      let find = res.rows.find((row) => row.name === item.name);
      item.drs = find?.drs;
    });
    part2.value.forEach((item) => {
      let find = res.rows.find((row) => row.name === item.name);
      item.drs = find?.drs;
    });
    part3.value.forEach((item) => {
      let find = res.rows.find((row) => row.name === item.name);
      item.drs = find?.drs;
    });
    part4.value.forEach((item) => {
      let find = res.rows.find((row) => row.name === item.name);
      item.drs = find?.drs;
    });
    message.info('填充完成');
  };

  // =====================================公共函数
  const httpGetData = async (date: Dayjs) => {
    show.value = false;
    let queryDate = dayjs(date).format('YYYY-MM-DD');
    const res = await defHttp.get({ url: '/jg/zhcb/query', params: { queryDate } });
    rows.value = res.rows;
    part1.value = res.rows.filter((item) => item.type == 'cl' || item.type == 'bj' || item.type == 'rl');
    part2.value = res.rows.filter((item) => item.type == 'd' || item.type == 's');
    part3.value = res.rows.filter((item) => item.type == 'gz' || item.type == 'gzxfy');
    part4.value = res.rows.filter((item) => item.type == 'unrelated' || item.type == 'zzfy' || item.type == 'zjf');
    isSave.value = true;
    firstWatch = true;
    show.value = true;
  };
  const presets = ref([
    { label: '昨天', value: dayjs().add(-1, 'd') },
    { label: '明天', value: dayjs().add(1, 'd') },
    { label: '上周', value: dayjs().add(-7, 'd') },
    { label: '上个月', value: dayjs().add(-1, 'month') },
  ]);
</script>
<style lang="less" scoped>
  @import url(@/views/cw/public/style/table.less);

  .grid-table > div {
    grid-template-columns: repeat(4, 1fr);
  }

  .grid-table {
    height: 100%;
  }

  :deep(.custom-center-text) {
    width: 50%;
  }
</style>
