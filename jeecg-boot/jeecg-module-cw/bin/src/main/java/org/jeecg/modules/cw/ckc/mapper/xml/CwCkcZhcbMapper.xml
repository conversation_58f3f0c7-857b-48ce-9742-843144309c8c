<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.cw.ckc.mapper.CwCkcZhcbMapper">


    <select id="queryRows" resultType="org.jeecg.modules.cw.ckc.entity.CwCkcRow">
        select ccd.name   as name,
               ccd.unit   as unit,
               ccd.type   as type,
               ccd.pjzh   as pjzh,
               ccd.remark as remark,
               ccm.pjdj   as pjdj,
               ccm.rys    as rys
        from cw_ckc_day ccd
                 right join cw_ckc_month ccm on ccd.name = ccm.name
        where ccd.record_time = #{queryDate}
          and date_format(ccm.record_time, '%Y-%m') = date_format(#{queryDate}, '%Y-%m');
    </select>
</mapper>