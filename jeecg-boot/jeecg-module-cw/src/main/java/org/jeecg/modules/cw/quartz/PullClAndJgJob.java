package org.jeecg.modules.cw.quartz;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrDay;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrDayService;
import org.jeecg.modules.cw.quartz.entity.DtsmmParam;
import org.jeecg.modules.cw.quartz.service.IPriceDataService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 每日执行获取前一天的产品产量和价格数据的Job
 */
@Slf4j
@Service
public class PullClAndJgJob implements Job {
    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwMnlrDayService mnlrDayService;
    
    @Autowired
    private IPriceDataService priceDataService;

    @Override
    public void execute(JobExecutionContext ctx) throws JobExecutionException {
        // 从Job参数中获取参数
        String parameter = (String) ctx.getJobDetail().getJobDataMap().get("parameter");
        JSONObject paramJson = JSONObject.parseObject(parameter);
        Date startDate = null;
        Date endDate = null;
        if (ObjectUtil.isNotEmpty(paramJson)) {
            startDate = paramJson.getDate("startDate");
            endDate = paramJson.getDate("endDate");
        }
        // 如果date为空，则获取前一天的日期
        if (startDate == null) {
            // 获取前一天的日期
            Date curTime = new Date();
            startDate = DateUtil.offsetDay(curTime, -1);
            endDate = curTime;
        }
        log.info("开始获取{}的产品价格和产量数据", startDate);
        
        for (Date date = startDate; date.before(endDate) || date.equals(endDate); date = DateUtil.offsetDay(date, 1)) {
            try {
                // 获取前一天的价格数据
                priceDataService.getPriceData(date);
                // 获取前一天的产量数据并更新价格数据中的产量
                priceDataService.getOutputDataAndUpdate(date);
            } catch (Exception e) {
                log.error("获取数据异常", e);
            }
        }
    }
}
