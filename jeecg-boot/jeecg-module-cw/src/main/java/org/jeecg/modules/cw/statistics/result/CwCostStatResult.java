package org.jeecg.modules.cw.statistics.result;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 总成本、吨矿成本、金属成本 统计结果
 */
@Data
public class CwCostStatResult {
    /** 维度：month | year */
    private String dimension;

    /** 周期字符串，如 2025-06 或 2025 */
    private String period;

    /** 总成本（万元）*/
    private BigDecimal totalCost;

    /** 吨矿成本（元/吨或其它单位，暂定） */
    private BigDecimal tonCost;

    /** 金属成本（暂时=总成本，后续可能调整） */
    private BigDecimal metalCost;

    /** 人均产值（万元/人，= 总产值 / 人数） */
    private BigDecimal avgOutput;

    /** 人均利润（万元/人，= 总利润 / 人数） */
    private BigDecimal avgProfit;
} 