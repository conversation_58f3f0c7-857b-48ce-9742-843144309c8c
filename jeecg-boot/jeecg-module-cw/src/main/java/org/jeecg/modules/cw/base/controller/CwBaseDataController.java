package org.jeecg.modules.cw.base.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.cw.base.entity.CwBaseData;
import org.jeecg.modules.cw.base.service.ICwKBaseService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.hutool.core.date.DateField;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 其他基础数据
 * @Author: jeecg-boot
 * @Date:   2025-01-08
 * @Version: V1.0
 */
@Api(tags="其他基础数据")
@RestController
@RequestMapping("/base/cwBaseData")
@Slf4j
public class CwBaseDataController extends JeecgController<CwBaseData, ICwKBaseService> {
	@Autowired
	private ICwKBaseService cwBaseDataService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwBaseData
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "其他基础数据-分页列表查询")
	@ApiOperation(value="其他基础数据-分页列表查询", notes="其他基础数据-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwBaseData>> queryPageList(CwBaseData cwBaseData,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwBaseData> queryWrapper = QueryGenerator.initQueryWrapper(cwBaseData, req.getParameterMap());
		Page<CwBaseData> page = new Page<CwBaseData>(pageNo, pageSize);
		IPage<CwBaseData> pageList = cwBaseDataService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwBaseData
	 * @return
	 */
	@AutoLog(value = "其他基础数据-添加")
	@ApiOperation(value="其他基础数据-添加", notes="其他基础数据-添加")
	@RequiresPermissions("base:cw_base_data:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwBaseData cwBaseData) {
		cwBaseDataService.save(cwBaseData);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwBaseData
	 * @return
	 */
	@AutoLog(value = "其他基础数据-编辑")
	@ApiOperation(value="其他基础数据-编辑", notes="其他基础数据-编辑")
	@RequiresPermissions("base:cw_base_data:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwBaseData cwBaseData) {
		cwBaseDataService.updateById(cwBaseData);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "其他基础数据-通过id删除")
	@ApiOperation(value="其他基础数据-通过id删除", notes="其他基础数据-通过id删除")
	@RequiresPermissions("base:cw_base_data:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwBaseDataService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "其他基础数据-批量删除")
	@ApiOperation(value="其他基础数据-批量删除", notes="其他基础数据-批量删除")
	@RequiresPermissions("base:cw_base_data:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwBaseDataService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "其他基础数据-通过id查询")
	@ApiOperation(value="其他基础数据-通过id查询", notes="其他基础数据-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwBaseData> queryById(@RequestParam(name="id",required=true) String id) {
		CwBaseData cwBaseData = cwBaseDataService.getById(id);
		if(cwBaseData==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwBaseData);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwBaseData
    */
    @RequiresPermissions("base:cw_base_data:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwBaseData cwBaseData) {
        return super.exportXls(request, cwBaseData, CwBaseData.class, "其他基础数据");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("base:cw_base_data:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwBaseData.class);
    }

	@AutoLog(value = "其他基础数据-设置")
	@ApiOperation(value="其他基础数据-设置", notes="其他基础数据-设置")
	@PostMapping(value = "/set/year")
	public Result<String> setYaerData(@RequestBody CwBaseData cwBaseData) {
		cwBaseDataService.setCwBaseData(cwBaseData.getName(), cwBaseData.getData(), cwBaseData.getRecordTime(), DateField.YEAR);
		return Result.OK("设置成功!");
	}

	@AutoLog(value = "其他基础数据-设置")
	@ApiOperation(value="其他基础数据-设置", notes="其他基础数据-设置")
	@PostMapping(value = "/set/month")
	public Result<String> setMonthData(@RequestBody CwBaseData cwBaseData) {
		cwBaseDataService.setCwBaseData(cwBaseData.getName(), cwBaseData.getData(), cwBaseData.getRecordTime(), DateField.MONTH);
		return Result.OK("设置成功!");
	}

	@AutoLog(value = "其他基础数据-设置")
	@ApiOperation(value="其他基础数据-设置", notes="其他基础数据-设置")
	@PostMapping(value = "/set/day")
	public Result<String> setDayData(@RequestBody CwBaseData cwBaseData) {
		cwBaseDataService.setCwBaseData(cwBaseData.getName(), cwBaseData.getData(), cwBaseData.getRecordTime(), DateField.DAY_OF_YEAR);
		return Result.OK("设置成功!");
	}
}
