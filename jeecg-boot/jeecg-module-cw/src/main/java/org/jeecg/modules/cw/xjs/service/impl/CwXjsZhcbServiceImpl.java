package org.jeecg.modules.cw.xjs.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.ds.entity.CwDsDay;
import org.jeecg.modules.cw.xjs.entity.CwXjsDay;
import org.jeecg.modules.cw.xjs.entity.CwXjsMonth;
import org.jeecg.modules.cw.xjs.entity.CwXjsRow;
import org.jeecg.modules.cw.xjs.param.CwXjsZhcbSumbitParam;
import org.jeecg.modules.cw.xjs.result.CwXjsZhcbListResult;
import org.jeecg.modules.cw.xjs.service.ICwXjsDayService;
import org.jeecg.modules.cw.xjs.service.ICwXjsMonthService;
import org.jeecg.modules.cw.xjs.service.ICwXjsZhcbService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 检化中心-日填报
 * @Author: jeecg-boot
 * @Date:   2024-12-06
 * @Version: V1.0
 */
@Service
public class CwXjsZhcbServiceImpl implements ICwXjsZhcbService {

    private static final String DICT_TYPE = "xjs";

    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwXjsDayService xjsDayService;
    @Resource
    private ICwXjsMonthService xjsMonthService;


    @Override
    public CwXjsZhcbListResult query(Date queryDate) {
        CwXjsZhcbListResult result = new CwXjsZhcbListResult();
        result.setQueryDate(queryDate);
        // 项目字典
        List<CwNameDict> dict = nameDictService.queryList(DICT_TYPE);
        // 已有列表
        List<CwXjsDay> allDays = xjsDayService.lambdaQuery()
                .between(CwXjsDay::getRecordTime, DateUtil.beginOfMonth(queryDate), DateUtil.endOfDay(queryDate))
                .list();
        List<CwXjsDay> days = allDays.stream()
                .filter(d -> DateUtil.isSameDay(queryDate, d.getRecordTime()))
                .collect(Collectors.toList());
        List<CwXjsMonth> months = xjsMonthService.lambdaQuery()
                .ge(CwXjsMonth::getRecordTime, DateUtil.beginOfMonth(queryDate))
                .le(CwXjsMonth::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();
        // 合并数据
        List<CwXjsRow> resRows = new ArrayList<>();
        Map<String, BigDecimal> pjdj = new HashMap<>();
        for (CwNameDict d : dict) {
            CwXjsRow row = new CwXjsRow();
            BeanUtil.copyProperties(d, row);
            // 数据
            months.stream().filter(d1 -> d.getName().equals(d1.getName()))
                    .findFirst()
                    .ifPresent(d1 -> {
                        row.setYjh(d1.getYjh());
                    });
            days.stream().filter(d1 -> d.getName().equals(d1.getName()))
                    .findFirst()
                    .ifPresent(d1 -> {
                        row.setDrs(d1.getDrs());
                        row.setRemark(d1.getRemark());
                    });
            // 月累计 注！不包括当天
            BigDecimal sum = allDays.stream()
                    .filter(v -> d.getName().equals(v.getName())
                            && ObjectUtil.isNotEmpty(v.getDrs())
                            && !DateUtil.isSameDay(v.getRecordTime(), queryDate))
                    .map((CwXjsDay::getDrs))
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            row.setYlj(sum);
            resRows.add(row);
        }
        result.setRows(resRows);
        // 结果
        return result;
    }

    @Override
    public void submit(CwXjsZhcbSumbitParam param) {
        Date submitDate = param.getSubmitDate();
        // 更新行数据
        List<CwXjsRow> rows = param.getRows();
        xjsDayService.remove(new LambdaQueryWrapper<>(CwXjsDay.class).eq(CwXjsDay::getRecordTime, submitDate));
        xjsMonthService.remove(new LambdaQueryWrapper<>(CwXjsMonth.class)
                .ge(CwXjsMonth::getRecordTime, DateUtil.beginOfMonth(submitDate))
                .le(CwXjsMonth::getRecordTime, DateUtil.endOfMonth(submitDate)));
        ArrayList<CwXjsDay> days = new ArrayList<>();
        for (CwXjsRow row : rows) {
            CwXjsDay day = new CwXjsDay();
            BeanUtil.copyProperties(row, day);
            day.setRecordTime(submitDate);
            days.add(day);
        }
        xjsDayService.saveBatch(days);
        ArrayList<CwXjsMonth> months = new ArrayList<>();
        for (CwXjsRow row : rows) {
            CwXjsMonth month = new CwXjsMonth();
            BeanUtil.copyProperties(row, month);
            month.setRecordTime(submitDate);
            months.add(month);
        }
        xjsMonthService.saveBatch(months);
    }

    @Override
    public CwXjsZhcbListResult autoFill(Date queryDate) {
        // 1. 新的填充逻辑
        Date dateToCopyFrom = null;
        int dayOfMonth = DateUtil.dayOfMonth(queryDate);

        // 最后一天不填充
        if (DateUtil.isLastDayOfMonth(queryDate)) {
            // 当天是最后一天时，不进行特殊填充
        } else if (dayOfMonth >= 2 && dayOfMonth <= 14) {
            // 2-14号，用1号填充
            dateToCopyFrom = DateUtil.beginOfMonth(queryDate);
        } else if (dayOfMonth >= 16 && dayOfMonth <= 30) {
            // 16-30号，用15号填充
            dateToCopyFrom = DateUtil.date(queryDate).setField(DateField.DAY_OF_MONTH, 15).toJdkDate();
        }

        if (dateToCopyFrom != null) {
            long count = xjsDayService.lambdaQuery()
                    .between(CwXjsDay::getRecordTime, DateUtil.beginOfDay(dateToCopyFrom), DateUtil.endOfDay(dateToCopyFrom))
                    .count();
            if (count > 0) {
                // 找到数据，立即使用这个日期进行查询并返回
                return query(dateToCopyFrom);
            }
        }

        // 2. 如果新逻辑未命中或源数据不存在，则默认查询当天
        return query(queryDate);
    }
}
