package org.jeecg.modules.cw.mnlr.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.DateRange;
import cn.hutool.core.date.DateField;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrStatisticsDay;
import org.jeecg.modules.cw.mnlr.mapper.CwMnlrStatisticsDayMapper;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrStatisticsDayService;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrDay;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrDayService;
import org.jeecg.modules.cw.mnlr.result.CwMnlrDayQueryResult;
import org.jeecg.modules.cw.mnlr.util.MnlrStatisticsUtil;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.jeecg.modules.cw.base.service.ICwKBaseService;
import org.springframework.context.annotation.Lazy;

/**
 * @Description: 矿模拟利润统计数据（日）
 * @Author: jeecg-boot
 * @Date:   2025-02-20
 * @Version: V1.0
 */
@Service
public class CwMnlrStatisticsDayServiceImpl extends ServiceImpl<CwMnlrStatisticsDayMapper, CwMnlrStatisticsDay> implements ICwMnlrStatisticsDayService {

    @Resource
    private ICwMnlrDayService mnlrDayService;

    @Lazy
    @Resource
    private ICwKBaseService kBaseService;

    @Override
    public void addOrEdit(CwMnlrStatisticsDay entity) {
        if (ObjectUtil.isEmpty(entity.getRecordTime())) {
            return;
        }
        List<CwMnlrStatisticsDay> list =
                this.lambdaQuery().eq(CwMnlrStatisticsDay::getRecordTime, entity.getRecordTime()).list();
        if (list.isEmpty()) {
            this.save(entity);
        } else {
            this.lambdaUpdate().eq(CwMnlrStatisticsDay::getRecordTime, entity.getRecordTime())
                    .setEntity(entity);
        }
    }

    @Override
    public BigDecimal getDayProfit(Date date) {
        if (date == null) {
            return BigDecimal.ZERO;
        }
        CwMnlrStatisticsDay one = this.lambdaQuery()
                .eq(CwMnlrStatisticsDay::getRecordTime, DateUtil.beginOfDay(date))
                .one();
        return one != null && one.getMnlr() != null ? one.getMnlr() : BigDecimal.ZERO;
    }

    @Override
    public BigDecimal sumMnlrRange(Date start, Date end) {
        if (start == null || end == null || start.after(end)) {
            return BigDecimal.ZERO;
        }
        // 一次性查出区间内所有日统计，内存累加，避免多次数据库访问
        return this.lambdaQuery()
                .ge(CwMnlrStatisticsDay::getRecordTime, DateUtil.beginOfDay(start))
                .le(CwMnlrStatisticsDay::getRecordTime, DateUtil.endOfDay(end))
                .list()
                .stream()
                .map(CwMnlrStatisticsDay::getMnlr)
                .filter(ObjectUtil::isNotNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal sumSlRange(Date start, Date end) {
        if (start == null || end == null || start.after(end)) {
            return BigDecimal.ZERO;
        }
        // 一次性查出区间内所有日统计，内存累加销售收入
        return this.lambdaQuery()
                .ge(CwMnlrStatisticsDay::getRecordTime, DateUtil.beginOfDay(start))
                .le(CwMnlrStatisticsDay::getRecordTime, DateUtil.endOfDay(end))
                .list()
                .stream()
                .map(item -> {
                    String sl = item.getSl();
                    if (ObjectUtil.isNotEmpty(sl)) {
                        try {
                            return new BigDecimal(sl);
                        } catch (NumberFormatException e) {
                            return BigDecimal.ZERO;
                        }
                    }
                    return BigDecimal.ZERO;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public void recalcDay(Date date) {
        if (date == null) {
            return;
        }
        Date day = DateUtil.beginOfDay(date);
        CwMnlrDayQueryResult result = mnlrDayService.queryByDate(day);

        if (result == null || result.getRows() == null || result.getRows().isEmpty()) {
            // 若无数据，则删除原有统计记录
            this.lambdaUpdate().eq(CwMnlrStatisticsDay::getRecordTime, day).remove();
            return;
        }
        CwMnlrStatisticsDay statisticsDay = MnlrStatisticsUtil.calculateStatistics(result, day);
        if (statisticsDay != null) {
            // 使用新的总成本逻辑
            BigDecimal cbSum = kBaseService.getTotalDrs(day);
            statisticsDay.setCb(cbSum);

            // 重新计算 mnlr 与 jhb
            BigDecimal sl = new BigDecimal(statisticsDay.getSl());
            BigDecimal qtfy = statisticsDay.getQt();
            BigDecimal djlr = statisticsDay.getDjlr();
            BigDecimal gsjh = statisticsDay.getGsjh();

            BigDecimal mnlr = sl.subtract(cbSum).subtract(qtfy).add(djlr);
            BigDecimal jhb = mnlr.subtract(gsjh);

            statisticsDay.setMnlr(mnlr);
            statisticsDay.setJhb(jhb);

            CwMnlrStatisticsDay existing = this.lambdaQuery()
                    .eq(CwMnlrStatisticsDay::getRecordTime, day)
                    .one();
            if (existing == null) {
                this.save(statisticsDay);
            } else {
                statisticsDay.setId(existing.getId());
                this.updateById(statisticsDay);
            }
        }
    }

    @Override
    public void recalcMonth(int year, int month) {
        // 月份参数: 1-12
        if (month < 1 || month > 12) {
            throw new IllegalArgumentException("month must be 1-12");
        }
        Date firstDay = DateUtil.parse(String.format("%d-%02d-01", year, month));
        Date lastDay = DateUtil.endOfMonth(firstDay);
        DateRange range = DateUtil.range(firstDay, lastDay, DateField.DAY_OF_MONTH);
        range.forEach(this::recalcDay);
    }

    @Override
    public void recalcAll() {
        // 查出所有已存在的 CwMnlrDay 记录日期
        List<Date> recordDates = mnlrDayService.lambdaQuery()
                .select(CwMnlrDay::getRecordTime)
                .list()
                .stream()
                .map(CwMnlrDay::getRecordTime)
                .map(DateUtil::beginOfDay)
                .collect(Collectors.toList());
        // 使用 Set 去重
        Set<Date> uniqueDates = new HashSet<>(recordDates);
        uniqueDates.forEach(this::recalcDay);
    }

    @Override
    public void recalcRange(Date start, Date end) {
        if (start == null || end == null) {
            return;
        }
        Date begin = DateUtil.beginOfDay(start);
        Date finish = DateUtil.beginOfDay(end);
        for (Date d = begin; !d.after(finish); d = DateUtil.offsetDay(d, 1)) {
            recalcDay(d);
        }
    }
}
